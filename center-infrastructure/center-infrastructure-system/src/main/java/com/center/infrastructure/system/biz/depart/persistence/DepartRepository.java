package com.center.infrastructure.system.biz.depart.persistence;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface DepartRepository extends JpaRepository<DepartModel, Long> {

    /**
     * 根据部门名称、上级部门ID查询是否存在重复的部门
     *
     * @param name     部门名称
     * @param parentId 上级部门ID
     * @return 是否存在重复部门
     */
    boolean existsByDepartNameAndParentId(String name, Long parentId);

    /**
     * 根据部门名称、上级部门ID、租户ID和部门ID（排除当前部门ID）查询是否存在重复的部门
     *
     * @param name     部门名称
     * @param parentId 上级部门ID
     * @param tenantId 租户ID
     * @param id       部门ID
     * @return 是否存在重复部门
     */
    boolean existsByDepartNameAndParentIdAndTenantIdAndIdNot(String name, Long parentId, Long tenantId, Long id);

    /**
     * 根据上级部门ID和租户ID查询部门列表
     *
     * @param parentId 上级部门ID
     * @param tenantId 租户ID
     * @return 部门列表
     */
    List<DepartModel> findByParentIdAndTenantId(Long parentId, Long tenantId);

    /**
     * 根据租户ID查询部门列表
     *
     * @param tenantId 租户ID
     * @return 部门列表
     */
    List<DepartModel> findByTenantId(Long tenantId);


    /**
     * 判断目标部门是否是当前部门或下级部门
     *
     * @param userDeptId   用户所在部门ID
     * @param targetDeptId 目标部门ID
     * @return 是否在权限范围内
     */
    @Query("SELECT COUNT(d) > 0 FROM DepartModel d WHERE d.parentId = ?1 AND d.id = ?2")
    boolean existsByParentId(Long userDeptId, Long targetDeptId);

    /**
     * 获取用户部门及其下级部门的ID列表
     *
     * @param userDeptId 用户部门ID
     * @return 部门ID列表
     */
    @Query("SELECT d.id FROM DepartModel d WHERE d.parentId = ?1")
    List<Long> findAllByParentId(Long userDeptId);

    List<DepartModel> findByTenantIdAndParentIdNull(Long tenantId);

    List<DepartModel> findByTenantIdAndParentId(Long tenantId, Long parentId);

    List<DepartModel> findByTenantIdAndParentIdIn(Long tenantId, List<Long> ids);

    boolean existsByParentId(Long parentId);


    List<DepartModel> findByTenantIdAndIdIn(Long tenantId, List<Long> ids);

    Integer deleteAllByTenantId(Long tenantId);

    List<DepartModel> findByParentId(Long id);

    @Query(nativeQuery = true, value = "WITH RECURSIVE subordinates AS (\n" +
            "    SELECT id, name, parent_id\n" +
            "    FROM center_depart\n" +
            "    WHERE id = ?1 \n" +
            "    UNION ALL\n" +
            "    SELECT e.id, e.name, e.parent_id\n" +
            "    FROM center_depart e\n" +
            "    INNER JOIN subordinates s ON e.parent_id = s.id\n" +
            ")\n" +
            "SELECT * FROM subordinates;")
    List<Long> selectSubById(Long id);
    List<DepartModel> findAllByParentIdNot(Long id);

    List<DepartModel> findAllByTenantId(Long id);
}
