package com.center.emergency.biz.apikeys.service;

import com.center.emergency.biz.apikeys.pojo.*;
import com.center.framework.common.exception.ServiceException;
import com.center.framework.web.pojo.PageParam;
import com.center.framework.web.pojo.PageResult;

public interface ApiKeysService {

    /**
     * 创建新的 API Key。
     *
     * @param req 创建 API Key 的请求参数，包括 apiKeyName、apiKey、有效期等字段
     * @return 创建后的 API Key 主键 ID
     * @throws ServiceException 如果名称重复或 API Key 已存在，抛出业务异常
     */
    Long createApiKey(ApiKeyCreateReq req);

    /**
     * 生成一个长度为 20 的随机 API Key 字符串。
     * <p>
     * 字符由预定义的 {@code CHARACTERS} 字符集随机组成，确保密钥安全性。
     *
     * @return 随机生成的 API Key 字符串
     */
    String generatorKey();

    /**
     * 删除指定 ID 的 API Key。
     * <p>
     * 此方法会先检查该 API Key 是否存在，并验证当前登录租户是否有删除权限。
     *
     * @param id API Key 的主键 ID
     * @throws ServiceException 如果 API Key 不存在或无权限删除，抛出业务异常
     */
    void deleteApiKey(Long id);

    /**
     * 更新指定 API Key 的信息。
     * <p>
     * 仅允许更新当前租户下的 API Key，且不允许修改实际的 {@code apiKey} 值本身。
     * 会校验名称重复、租户权限、以及更新字段的合法性。
     *
     * @param req 更新请求对象，包含 API Key ID、新名称等更新信息
     * @throws ServiceException 如果 API Key 不存在、无权限或数据冲突，抛出业务异常
     */
    void updateApiKey(ApiKeyUpdateReq req);


    /**
     * 获取指定 ID 的 API Key 详情（含脱敏后的密钥）。
     *
     * @param id API Key 主键 ID
     * @return 包含 API Key 详情的 DTO，对 {@code apiKey} 字段已脱敏（仅保留前后 4 位）
     * @throws ServiceException 如果 API Key 不存在或无权限访问，则抛出业务异常
     */
    ApiKeyResp getApiKeyDetail(Long id);

    /**
     * 分页查询当前租户下的所有 API Key 列表，并对每条记录的密钥进行脱敏处理。
     *
     * @param req 分页参数，包括 pageNo、pageSize
     * @return 分页结果，DTO 列表中每个 {@code apiKey} 字段已脱敏
     */
    PageResult<ApiKeyPageResp> pageApiKey(PageParam req);

    /**
     * 校验给定的 API Key 字符串是否有效（存在且未过期）。
     *
     * @param req 完整的 API Key 校验请求
     * @return {@code true} 表示有效
     * @throws ServiceException 如果不存在多于一条匹配记录或该密钥已过期，则抛出业务异常
     */
    ValidateResp validateApiKey(ApiKeyValidateReq req);
}
