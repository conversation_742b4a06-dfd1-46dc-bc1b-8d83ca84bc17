package com.center.emergency.biz.apikeys;


import com.center.emergency.biz.apikeys.pojo.*;
import com.center.emergency.biz.apikeys.service.ApiKeysService;
import com.center.framework.web.annotation.enumconvert.EnumConvertPoint;
import com.center.framework.web.pojo.CommonResult;
import com.center.framework.web.pojo.PageParam;
import com.center.framework.web.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@Tag(name = "API Keys管理", description = "提供API Keys创建、更新、删除、查询功能")
@RequestMapping("/api_keys")
@Validated
public class ApiKeysController {

    @Resource
    private ApiKeysService apiKeysService;

    @PostMapping("/create")
    @Operation(summary = "创建API Key")
    @EnumConvertPoint
    public CommonResult<Long> createApiKey(@RequestBody @Validated ApiKeyCreateReq req) {
        Long id = apiKeysService.createApiKey(req);
        return CommonResult.success(id,"创建成功");
    }

    @PostMapping("/delete/{id}")
    @Operation(summary = "删除API Key")
    public CommonResult<Long> deleteAPIKey(@PathVariable("id") Long id) {
        apiKeysService.deleteApiKey(id);
        return CommonResult.success(id,"删除成功");
    }

    @PostMapping("/generator")
    @Operation(summary = "自动生成API Key")
    public CommonResult<String> generatorKey() {
        String key = apiKeysService.generatorKey();
        return CommonResult.success(key,"生成成功");
    }

    @PostMapping("/validate")
    @Operation(summary = "校验API Key的有效性")
    public CommonResult<ValidateResp> validateApiKey(@RequestBody ApiKeyValidateReq req) {
        return CommonResult.success(apiKeysService.validateApiKey(req));
    }

    @PostMapping("/update")
    @Operation(summary = "更新API Key服务")
    @EnumConvertPoint
    public CommonResult<String> updateApiKey(@RequestBody @Validated ApiKeyUpdateReq req) {
        apiKeysService.updateApiKey(req);
        return CommonResult.successWithMessageOnly("更新成功");
    }

    @GetMapping("/get/{id}")
    @Operation(summary = "查询API Key详情")
    public CommonResult<ApiKeyResp> getApiKeyDetail(@PathVariable("id") Long id) {
        ApiKeyResp resp = apiKeysService.getApiKeyDetail(id);
        return CommonResult.success(resp, "查询成功");
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询API Key")
    @EnumConvertPoint
    public CommonResult<PageResult<ApiKeyPageResp>> pageApiKey(PageParam req) {
        return CommonResult.success(apiKeysService.pageApiKey(req));
    }

}