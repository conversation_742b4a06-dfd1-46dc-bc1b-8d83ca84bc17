package com.center.emergency.biz.robot.pojo;

import com.center.emergency.common.enumeration.AnswerModeEnum;
import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import com.center.emergency.common.enumeration.SearchModeEnum;
import com.center.framework.common.pojo.IdAndValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class RobotPreviewResp extends RobotBase {

    @Schema(description = "机器人ID")
    private Long id;

    @Schema(description = "机器人LogoId")
    private Long logoId;

    // 关联知识库
    @Schema(description = "关联的大模型回答策略",example = "")
    private List<IdAndValue> answerStrategyList;

    // 系统提示词
    @Schema(description = "机器人系统提示词", example = "你是一个专业的医疗咨询机器人")
    private String systemPrompt;

    // 对话示例
    @Schema(description = "机器人对话示例", example = "用户：你好 机器人：您好，有什么我可以帮您的吗？")
    private String dialogueExamples;

    // 检索模式
    @Schema(description = "检索模式：VECTOR / TEXT / HYBRID", example = "HYBRID")
    private SearchModeEnum searchMode;

    // 问答模式
    @Schema(description = "问答模式：ONLY_KB / KB_FIRST_MODEL / ONLY_QA", example = "KB_FIRST_MODEL")
    private AnswerModeEnum answerMode;

    // 文本匹配相似度阈值
    @Schema(description = "文本匹配相似度阈值", example = "0.80")
    private BigDecimal similarityThreshold;

    // 最大召回数量
    @Schema(description = "最大召回数量", example = "3")
    private Integer maxHits;

    @Schema(description = "Agent问答策略",example = "MCP")
    private AnswerStrategyEnum answerStrategy;

}
