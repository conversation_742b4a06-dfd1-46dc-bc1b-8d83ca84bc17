package com.center.emergency.biz.robot.service;

import com.center.emergency.biz.chat.pojo.ChatVO;
import com.center.emergency.biz.chat.pojo.RobotModelDTO;
import com.center.emergency.biz.mcp.persistence.QMcpModel;
import com.center.emergency.biz.robot.persitence.QRobotKnowledgeModel;
import com.center.emergency.biz.modelgroup.service.ModelGroupService;
import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import com.center.framework.common.pojo.IdAndValue;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.*;

@Component("MCP")
@Slf4j
public class McpServiceForAnswerStrategy extends AbstractAnswerStrategy implements AnswerStrategyService{

    @Resource
    private ModelGroupService modelGroupService;

    @Override
    public List<IdAndValue> listAnswerStrategyIds(Long robotId) {
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;
        QMcpModel qMcpModel = QMcpModel.mcpModel;
        List<IdAndValue> result = queryFactory.select(Projections.bean(
                        IdAndValue.class,
                        qRobotKnowledgeModel.kbId.as("id"),
                        qMcpModel.mcpName.as("value")
                ))
                .from(qRobotKnowledgeModel).join(qMcpModel).on(qRobotKnowledgeModel.kbId.eq(qMcpModel.id)).
                where(qRobotKnowledgeModel.robotId.eq(robotId))
                .fetch();

        return result;
    }

    /**
     * MCP策略的参数构建 - 实现MCP特定的参数构建逻辑
     * 注意：MCP策略不使用kbIds和fileIds，这些参数会被忽略
     */
    @Override
    public HashMap<String, Object> buildChatParams(ChatVO chatVO, Set<String> kbIds, List<String> fileIds,
                                                   List<Map<String, String>> tags, Boolean isSimulate,
                                                   Long modelId, JPAQueryFactory jpaQueryFactory) {
        log.info("构建MCP策略对话参数: chatVO={}, modelId={}, isSimulate={} (忽略kbIds和fileIds)",
                chatVO.getQuestion(), modelId, isSimulate);

        // 1. 收集MCP服务配置
        List<Map<String, Object>> dynamicServers = collectMcpServers(chatVO.getRobotId(), jpaQueryFactory);

        // 2. 构建MCP策略基础参数
        HashMap<String, Object> param = buildMcpBaseParams(chatVO);

        // 3. 查询并添加模型配置
        if (modelId != null) {
            RobotModelDTO robotModelDTO = queryRobotModelConfig(chatVO.getRobotId(), modelId, jpaQueryFactory);
            // MCP策略只需要基础模型配置，不需要策略特有参数
            addBaseModelConfigParams(param, robotModelDTO);
        } else {
            // 使用系统默认模型配置
            Map<String, Object> defaultModelConfig = modelGroupService.getSystemDefaultModelConfig();
            param.putAll(defaultModelConfig);
        }

        // 4. 添加MCP特有参数
        param.put("dynamic_servers", dynamicServers);
        param.put("max_tool_rounds", 10); // 默认最大工具调用轮次

        log.info("MCP策略参数构建完成，dynamic_servers数量: {}", dynamicServers.size());
        return param;
    }

    /**
     * MCP策略的资源收集和参数构建 - 实现MCP特定的参数构建逻辑
     */
    @Override
    public HashMap<String, Object> buildChatParamsWithResourceCollection(ChatVO chatVO, Boolean isSimulate,
                                                                         Long modelGroupId, JPAQueryFactory jpaQueryFactory) {
        log.info("MCP策略自主收集资源并构建参数: chatVO={}, modelGroupId={}, isSimulate={}",
                chatVO.getQuestion(), modelGroupId, isSimulate);

        // 1. 收集MCP服务配置
        List<Map<String, Object>> dynamicServers = collectMcpServers(chatVO.getRobotId(), jpaQueryFactory);

        // 2. 构建MCP策略基础参数
        HashMap<String, Object> param = buildMcpBaseParams(chatVO);

        // 3. 添加模型组配置
        addModelGroupParams(param, modelGroupId);

        // 4. 添加MCP特有参数
        param.put("dynamic_servers", dynamicServers);
        param.put("max_tool_rounds", 10); // 默认最大工具调用轮次

        log.info("MCP策略参数构建完成，dynamic_servers数量: {}", dynamicServers.size());
        return param;
    }

    /**
     * 构建MCP策略的基础参数
     * 包含MCP策略特有的参数格式
     */
    private HashMap<String, Object> buildMcpBaseParams(ChatVO chatVO) {
        HashMap<String, Object> param = buildCommonBaseParams(chatVO);

        // MCP策略特有参数（使用query字段而不是question）
        param.put("query", chatVO.getQuestion()); // MCP策略使用query字段

        return param;
    }

    /**
     * 收集机器人关联的MCP服务配置
     */
    private List<Map<String, Object>> collectMcpServers(Long robotId, JPAQueryFactory jpaQueryFactory) {
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;
        QMcpModel qMcpModel = QMcpModel.mcpModel;

        // 查询机器人关联的MCP服务
        List<com.querydsl.core.Tuple> mcpTuples = jpaQueryFactory
                .select(qMcpModel.serverName, qMcpModel.serverUrl, qMcpModel.mcpDesc)
                .from(qRobotKnowledgeModel)
                .join(qMcpModel).on(qRobotKnowledgeModel.kbId.eq(qMcpModel.id))
                .where(qRobotKnowledgeModel.robotId.eq(robotId)
                        .and(qRobotKnowledgeModel.answerStrategy.eq(AnswerStrategyEnum.MCP)))
                .fetch();

        // 转换为dynamic_servers格式
        List<Map<String, Object>> dynamicServers = new ArrayList<>();
        for (com.querydsl.core.Tuple tuple : mcpTuples) {
            Map<String, Object> server = new HashMap<>();
            server.put("server_key", tuple.get(qMcpModel.serverName));
            server.put("url", tuple.get(qMcpModel.serverUrl));
            server.put("description", tuple.get(qMcpModel.mcpDesc));
            server.put("make_default", false);
            dynamicServers.add(server);
        }

        log.info("收集到{}个MCP服务配置", dynamicServers.size());
        return dynamicServers;
    }


}
