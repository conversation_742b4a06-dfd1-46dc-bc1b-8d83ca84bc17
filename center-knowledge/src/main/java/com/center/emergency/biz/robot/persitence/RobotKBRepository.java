package com.center.emergency.biz.robot.persitence;

import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import com.center.framework.db.core.JoinFetchCapableQueryDslJpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RobotKBRepository extends JoinFetchCapableQueryDslJpaRepository<RobotKnowledgeModel,Long> {

    void deleteByRobotId(Long robotId);

    // 检查是否有机器人引用该知识库
    boolean existsByKbId(Long kbId);

    List<RobotKnowledgeModel> findByRobotIdAndAnswerStrategy(Long robotId, AnswerStrategyEnum answerStrategy);

    RobotKnowledgeModel findDistinctByRobotId(Long robotId);
}
